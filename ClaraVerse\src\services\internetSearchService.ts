/**
 * 🔍 Internet Search Service - SearXNG Integration
 * Service pour effectuer des recherches internet via SearXNG
 * Optimisé pour WeMa IA avec gestion d'erreurs et fallbacks
 */

export interface SearchResult {
  title: string;
  url: string;
  content: string;
  snippet?: string;
  publishedDate?: string;
  engine?: string;
  category?: string;
  score?: number;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  suggestions?: string[];
  totalResults?: number;
  searchTime?: number;
  engines?: string[];
  error?: string;
}

export interface SearchOptions {
  categories?: string[]; // ['general', 'news', 'images', 'videos', 'science', 'it']
  engines?: string[]; // ['google', 'bing', 'duckduckgo', 'wikipedia', etc.]
  language?: string; // 'fr', 'en', etc.
  timeRange?: 'day' | 'month' | 'year';
  safeSearch?: 0 | 1 | 2; // 0=off, 1=moderate, 2=strict
  maxResults?: number;
  timeout?: number;
}

export interface SearchProgressData {
  formattedResult: string;
  sources: string[];
  totalResults: number;
  searchTime: number;
}

class InternetSearchService {
  private searxngUrl: string;
  private defaultOptions: SearchOptions;
  private isAvailable: boolean = false;

  constructor() {
    // Configuration par défaut - utilise le backend Python
    this.searxngUrl = 'http://localhost:5001'; // Backend Python principal
    this.defaultOptions = {
      categories: ['general'],
      language: 'fr',
      safeSearch: 1,
      maxResults: 10,
      timeout: 10000
    };

    this.checkAvailability();
  }

  /**
   * Vérifier si le service de recherche est disponible
   */
  private async checkAvailability(): Promise<void> {
    try {
      const response = await fetch(`${this.searxngUrl}/search/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(15000) // Augmenté à 15 secondes
      });
      const data = await response.json();
      this.isAvailable = response.ok && data.status === 'available';
      console.log(`🔍 Service de recherche ${this.isAvailable ? 'disponible' : 'indisponible'} sur ${this.searxngUrl}`);
    } catch (error) {
      this.isAvailable = false;
      console.warn('🔍 Service de recherche non disponible:', error);
    }
  }

  /**
   * Configurer l'URL du backend
   */
  public configure(url: string): void {
    this.searxngUrl = url.replace(/\/$/, ''); // Supprimer le slash final
    this.checkAvailability();
  }

  /**
   * Vérifier si le service est disponible
   */
  public async isServiceAvailable(): Promise<boolean> {
    if (!this.isAvailable) {
      await this.checkAvailability();
    }
    return this.isAvailable;
  }

  /**
   * Effectuer une recherche internet
   */
  public async search(query: string, options: SearchOptions = {}): Promise<SearchResponse> {
    const startTime = Date.now();
    
    try {
      // Vérifier la disponibilité
      if (!await this.isServiceAvailable()) {
        throw new Error('Service de recherche non disponible');
      }

      // Fusionner les options
      const searchOptions = { ...this.defaultOptions, ...options };

      // Préparer les données pour le backend
      const requestData = {
        query,
        search_type: this.mapSearchType(searchOptions.categories?.[0] || 'general'),
        max_results: searchOptions.maxResults || 6,
        time_range: searchOptions.timeRange,
        language: searchOptions.language || 'fr'
      };

      console.log(`🔍 Recherche: "${query}" via backend Python`);

      // Effectuer la requête vers le backend
      const response = await fetch(`${this.searxngUrl}/search/internet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: AbortSignal.timeout(searchOptions.timeout || 30000) // 30 secondes pour le scraping
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const searchTime = Date.now() - startTime;

      // Vérifier si la recherche a réussi
      if (!data.success) {
        throw new Error(data.error || 'Erreur de recherche');
      }

      // Les résultats sont déjà formatés par le backend
      const formattedResult = data.result || '';
      const metadata = data.metadata || {};

      console.log(`✅ Recherche terminée: ${metadata.results_count || 0} résultats en ${searchTime}ms`);

      // Simuler la structure attendue pour la compatibilité
      const results: SearchResult[] = metadata.sources ?
        metadata.sources.map((url: string, index: number) => ({
          title: `Résultat ${index + 1}`,
          url,
          content: '',
          snippet: '',
        })) : [];

      return {
        query,
        results,
        suggestions: [],
        totalResults: metadata.results_count || 0,
        searchTime,
        engines: [],
        formattedResult // Ajouter le résultat formaté
      };

    } catch (error) {
      console.error('❌ Erreur recherche internet:', error);
      
      return {
        query,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown search error'
      };
    }
  }

  /**
   * Mapper les catégories vers les types de recherche du backend
   */
  private mapSearchType(category: string): string {
    switch (category) {
      case 'news': return 'news';
      case 'science': return 'scientific';
      case 'it': return 'technical';
      default: return 'general';
    }
  }

  /**
   * Recherche rapide avec données de progression pour l'animation
   */
  public async quickSearchWithProgress(query: string, onProgress?: (sources: string[], action: string) => void): Promise<SearchProgressData> {
    try {
      const startTime = Date.now();

      // Simuler la progression si callback fourni
      if (onProgress) {
        onProgress([], 'Connexion aux moteurs de recherche...');
      }

      const response = await this.search(query, {
        categories: ['general'],
        maxResults: 8,
        timeout: 25000
      });

      const searchTime = Date.now() - startTime;
      const sources = response.results.map(result => new URL(result.url).hostname);

      if (response.error) {
        return {
          formattedResult: `❌ Erreur de recherche: ${response.error}`,
          sources: [],
          totalResults: 0,
          searchTime
        };
      }

      // Utiliser le résultat formaté du backend si disponible
      let formattedResult: string;
      if ((response as any).formattedResult) {
        formattedResult = (response as any).formattedResult;
      } else if (response.results.length === 0) {
        formattedResult = `🔍 Aucun résultat trouvé pour: "${query}"`;
      } else {
        // Fallback: formater les résultats pour l'IA
        formattedResult = `🔍 **Résultats de recherche pour: "${query}"**\n\n`;
        response.results.forEach((result, index) => {
          formattedResult += `**${index + 1}. ${result.title}**\n`;
          formattedResult += `Source: ${result.url}\n`;
          formattedResult += `${result.content || result.snippet || 'Contenu non disponible'}\n\n`;
        });
      }

      return {
        formattedResult,
        sources: [...new Set(sources)], // Supprimer les doublons
        totalResults: response.results.length,
        searchTime
      };

    } catch (error) {
      console.error('❌ Erreur quickSearchWithProgress:', error);
      return {
        formattedResult: `❌ Erreur de recherche: ${error}`,
        sources: [],
        totalResults: 0,
        searchTime: 0
      };
    }
  }

  /**
   * Recherche rapide avec résultats formatés pour l'IA (compatibilité)
   */
  public async quickSearch(query: string): Promise<string> {
    const result = await this.quickSearchWithProgress(query);
    return result.formattedResult;
  }

  /**
   * Version legacy - maintenue pour compatibilité
   */
  private async quickSearchLegacy(query: string): Promise<string> {
    try {
      const response = await this.search(query, {
        categories: ['general'],
        maxResults: 5,
        timeout: 25000 // 25 secondes pour quickSearch
      });

      if (response.error) {
        return `❌ Erreur de recherche: ${response.error}`;
      }

      // Utiliser le résultat formaté du backend si disponible
      if ((response as any).formattedResult) {
        return (response as any).formattedResult;
      }

      if (response.results.length === 0) {
        return `🔍 Aucun résultat trouvé pour: "${query}"`;
      }

      // Fallback: formater les résultats pour l'IA
      let formattedResults = `🔍 **Résultats de recherche pour: "${query}"**\n\n`;

      response.results.forEach((result, index) => {
        formattedResults += `**${index + 1}. ${result.title}**\n`;
        formattedResults += `${result.snippet || result.content}\n`;
        formattedResults += `🔗 ${result.url}\n\n`;
      });

      formattedResults += `_Recherche effectuée en ${response.searchTime}ms_`;

      return formattedResults;

    } catch (error) {
      return `❌ Erreur lors de la recherche internet: ${error}`;
    }
  }

  /**
   * Recherche d'actualités
   */
  public async searchNews(query: string): Promise<SearchResponse> {
    try {
      const response = await fetch(`${this.searxngUrl}/search/news`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          query,
          time_range: 'month',
          max_results: 8
        }),
        signal: AbortSignal.timeout(10000)
      });

      const data = await response.json();
      return this.processBackendResponse(data, query);
    } catch (error) {
      return {
        query,
        results: [],
        suggestions: [],
        error: `Erreur recherche actualités: ${error}`
      };
    }
  }

  /**
   * Recherche académique/scientifique
   */
  public async searchScientific(query: string): Promise<SearchResponse> {
    return this.search(query, {
      categories: ['science'],
      maxResults: 6
    });
  }

  /**
   * Recherche technique/IT
   */
  public async searchTechnical(query: string): Promise<SearchResponse> {
    try {
      const response = await fetch(`${this.searxngUrl}/search/technical`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          query,
          max_results: 8
        }),
        signal: AbortSignal.timeout(10000)
      });

      const data = await response.json();
      return this.processBackendResponse(data, query);
    } catch (error) {
      return {
        query,
        results: [],
        suggestions: [],
        error: `Erreur recherche technique: ${error}`
      };
    }
  }

  /**
   * Traiter la réponse du backend
   */
  private processBackendResponse(data: any, query: string): SearchResponse {
    if (!data.success) {
      return {
        query,
        results: [],
        suggestions: [],
        error: data.error || 'Erreur de recherche'
      };
    }

    const metadata = data.metadata || {};
    const results: SearchResult[] = metadata.sources ?
      metadata.sources.map((url: string, index: number) => ({
        title: `Résultat ${index + 1}`,
        url,
        content: '',
        snippet: '',
      })) : [];

    return {
      query,
      results,
      suggestions: [],
      totalResults: metadata.results_count || 0,
      searchTime: 0,
      engines: [],
      formattedResult: data.result
    } as any;
  }
}

// Instance singleton
export const internetSearchService = new InternetSearchService();
export default internetSearchService;
