/**
 * 💬 Chat Manager - Gestion centralisée des conversations
 * 
 * Responsabilités :
 * - Envoi de messages avec/sans outils
 * - Gestion du streaming
 * - Préparation du contexte de conversation
 * - Sélection automatique des modèles
 * - Gestion des attachements
 */

import { AssistantAPIClient } from '../../../utils/AssistantAPIClient';
import type { ChatMessage } from '../../../utils/APIClient';
import type {
  ClaraMessage,
  ClaraProvider,
  ClaraAIConfig,
  ClaraFileAttachment
} from '../../../types/clara_assistant_types';
import type { Tool } from '../../../db';
import { RagManager } from '../../rag';
import { logger, LogCategory } from '../../../utils/logger';
import { getRagModeForSession } from '../../session/SessionRagModeService';

export interface ChatContext {
  message: string;
  config: ClaraAIConfig;
  systemPrompt?: string;
  conversationHistory?: ClaraMessage[];
  attachments?: ClaraFileAttachment[];
  ragContext?: string;
  sessionId?: string;
  ragMessages?: ClaraMessage[]; // 🚀 PERSISTANCE: Messages RAG pour l'historique
}

export class ChatManager {
  private client: AssistantAPIClient | null = null;
  private currentProvider: ClaraProvider | null = null;
  private ragManager: RagManager;
  private currentAbortController: AbortController | null = null;

  constructor() {
    this.ragManager = new RagManager();
  }

  /**
   * Mettre à jour le provider et le client API
   */
  public updateProvider(provider: ClaraProvider): void {
    this.currentProvider = provider;
    this.client = new AssistantAPIClient(provider.baseUrl || '', {
      apiKey: provider.apiKey || '',
      providerId: provider.id,
      type: provider.type  // 🚀 AJOUT DU TYPE POUR LA DÉLÉGATION D'INFÉRENCE
    });
    logger.info(LogCategory.PROVIDERS, `ChatManager: Provider mis à jour → ${provider.name} (type: ${provider.type})`);
  }

  /**
   * Arrêter la génération en cours
   */
  public stopGeneration(): void {
    if (this.currentAbortController) {
      console.log('🛑 Arrêt de la génération en cours...');
      this.currentAbortController.abort();
      this.currentAbortController = null;
      console.log('✅ Génération arrêtée');
    } else {
      console.log('ℹ️ Aucune génération en cours à arrêter');
    }
  }

  /**
   * Envoyer un message standard (sans outils)
   */
  public async sendStandardMessage(
    message: string,
    context: ChatContext,
    onContentChunk?: (content: string) => void
  ): Promise<ClaraMessage> {
    if (!this.client) {
      throw new Error('Aucun client API configuré');
    }

    console.log('💬 Envoi message standard');

    try {
      // Préparer les messages pour l'API
      const messages = await this.prepareMessages(context);
      
      // Sélectionner le modèle approprié
      const modelId = this.selectModel(context);
      
      // Options de génération
      const options = this.buildGenerationOptions(context.config);

      let responseContent = '';
      let totalTokens = 0;

      // Streaming ou non-streaming selon la configuration
      if (context.config.features.enableStreaming) {
        console.log('📡 Mode streaming activé');
        
        for await (const chunk of this.client.streamChat(modelId, messages, options)) {
          if (chunk.message?.content) {
            responseContent += chunk.message.content;
            if (onContentChunk) {
              onContentChunk(chunk.message.content);
            }
          }
          
          if (chunk.usage?.total_tokens) {
            totalTokens = chunk.usage.total_tokens;
          }
        }
      } else {
        console.log('📝 Mode non-streaming');
        
        const response = await this.client.sendChat(modelId, messages, options);
        responseContent = response.message?.content || '';
        totalTokens = response.usage?.total_tokens || 0;
        
        if (onContentChunk && responseContent) {
          onContentChunk(responseContent);
        }
      }

      // Créer le message de réponse Clara
      const responseMessage = this.createClaraMessage(responseContent, context.config, modelId, totalTokens);

      // 🔄 COMPRESSION POST-RÉPONSE ASYNCHRONE (non-bloquante) - ISOLÉE PAR SESSION
      if (context.conversationHistory && context.conversationHistory.length > 0) {
        // Lancer la compression en arrière-plan sans attendre
        this.schedulePostResponseCompression(
          context.conversationHistory,
          responseMessage,
          (context as any).compressionAnalysis,
          (context as any).sessionId,
          context.ragContext  // 🚀 INCLURE LE CONTEXTE RAG
        ).catch(error => {
          console.warn('⚠️ Erreur compression post-réponse:', error);
        });
      }

      return responseMessage;
      
    } catch (error) {
      console.error('❌ Erreur envoi message standard:', error);
      throw error;
    }
  }

  /**
   * Envoyer un message avec support des outils
   */
  public async sendMessageWithTools(
    message: string,
    context: ChatContext,
    tools: Tool[],
    onContentChunk?: (content: string) => void
  ): Promise<ClaraMessage> {
    if (!this.client) {
      throw new Error('Aucun client API configuré');
    }

    console.log(`🛠️ Envoi message avec ${tools.length} outils`);

    try {
      // Préparer les messages et outils
      const messages = await this.prepareMessages(context);
      const modelId = this.selectModel(context);
      const options = this.buildGenerationOptions(context.config);
      
      // Convertir les outils au format OpenAI
      const openAITools = this.convertToolsToOpenAIFormat(tools);

      let responseContent = '';
      let totalTokens = 0;
      let toolCalls: any[] = [];

      // Première requête avec outils
      if (context.config.features.enableStreaming && this.supportsStreamingWithTools()) {
        // Streaming avec outils (si supporté)
        const collectedToolCalls: any[] = [];
        
        for await (const chunk of this.client.streamChat(modelId, messages, options, openAITools)) {
          if (chunk.message?.content) {
            responseContent += chunk.message.content;
            if (onContentChunk) {
              onContentChunk(chunk.message.content);
            }
          }
          
          // Collecter les appels d'outils
          if (chunk.message?.tool_calls) {
            this.collectToolCalls(chunk.message.tool_calls, collectedToolCalls);
          }
          
          if (chunk.usage?.total_tokens) {
            totalTokens = chunk.usage.total_tokens;
          }
        }
        
        toolCalls = collectedToolCalls;
      } else {
        // Non-streaming avec outils
        const response = await this.client.sendChat(modelId, messages, options, openAITools);
        responseContent = response.message?.content || '';
        toolCalls = response.message?.tool_calls || [];
        totalTokens = response.usage?.total_tokens || 0;
        
        if (onContentChunk && responseContent) {
          onContentChunk(responseContent);
        }
      }

      // Exécuter les outils si nécessaire
      if (toolCalls.length > 0) {
        console.log(`🔧 Exécution de ${toolCalls.length} appels d'outils`);
        
        if (onContentChunk) {
          onContentChunk('\n\n🔧 **Exécution des outils...**\n\n');
        }

        // Ici on déléguerait à ToolManager
        // const toolResults = await toolManager.executeToolCalls(toolCalls);
        
        if (onContentChunk) {
          onContentChunk('✅ **Outils exécutés**\n\n');
        }
      }

      const responseMessage = this.createClaraMessage(responseContent, context.config, modelId, totalTokens, toolCalls);

      // 🔄 COMPRESSION POST-RÉPONSE ASYNCHRONE (non-bloquante) - ISOLÉE PAR SESSION
      if (context.conversationHistory && context.conversationHistory.length > 0) {
        this.schedulePostResponseCompression(
          context.conversationHistory,
          responseMessage,
          (context as any).compressionAnalysis,
          (context as any).sessionId,
          context.ragContext  // 🚀 INCLURE LE CONTEXTE RAG
        ).catch(error => {
          console.warn('⚠️ Erreur compression post-réponse:', error);
        });
      }

      return responseMessage;
      
    } catch (error) {
      console.error('❌ Erreur envoi message avec outils:', error);
      throw error;
    }
  }

  /**
   * Préparer le contexte de conversation - AVEC SESSION ID
   */
  public async prepareConversationContext(
    message: string,
    config: ClaraAIConfig,
    systemPrompt?: string,
    conversationHistory?: ClaraMessage[],
    attachments?: ClaraFileAttachment[],
    sessionId?: string
  ): Promise<ChatContext> {
    const context: ChatContext = {
      message,
      config,
      systemPrompt,
      conversationHistory,
      attachments,
      sessionId: sessionId || 'default'
    };

    // 🧠 GESTION INTELLIGENTE DU CONTEXTE - Documents sélectionnés + historique
    try {
      const { useDocumentStore } = await import('../../../stores/documentStore');
      const documentStore = useDocumentStore.getState();
      let documentsToProcess = [];

      // 1. Documents explicitement sélectionnés
      if (documentStore.selectedDocuments && documentStore.selectedDocuments.length > 0) {
        documentsToProcess.push(...documentStore.selectedDocuments);
        console.log(`📚 Documents sélectionnés: ${documentStore.selectedDocuments.length}`);
      }

      // 2. 🧠 DÉTECTION INTELLIGENTE - Documents dans l'historique de conversation
      if (conversationHistory && conversationHistory.length > 0) {
        const historyDocuments = this.extractDocumentsFromHistory(conversationHistory);
        if (historyDocuments.length > 0) {
          console.log(`🔍 Documents détectés dans l'historique: ${historyDocuments.length}`);

          // 🚨 DÉDUPLICATION AVANCÉE - Par ID, nom ET contenu
          const existingIds = new Set(documentsToProcess.map(d => d.id));
          const existingNames = new Set(documentsToProcess.map(d => d.filename || d.name));
          const existingContentHashes = new Set(documentsToProcess.map(d =>
            d.content ? d.content.substring(0, 200) : ''
          ));

          const newDocuments = historyDocuments.filter(d => {
            // Vérifier ID
            if (existingIds.has(d.id)) return false;

            // Vérifier nom de fichier
            if (existingNames.has(d.filename || d.name)) return false;

            // Vérifier contenu (premiers 200 caractères)
            const contentHash = d.content ? d.content.substring(0, 200) : '';
            if (contentHash && existingContentHashes.has(contentHash)) return false;

            return true;
          });

          if (newDocuments.length > 0) {
            documentsToProcess.push(...newDocuments);
            console.log(`➕ Ajout de ${newDocuments.length} documents de l'historique (${historyDocuments.length - newDocuments.length} doublons évités)`);
          } else {
            console.log(`⚠️ Tous les documents de l'historique sont des doublons`);
          }
        }
      }

      // 3. Traitement RAG si des documents sont disponibles ET mode performant
      // 🎯 VÉRIFICATION MODE RAG AVANT TRAITEMENT
      const ragFastModeStr = typeof window !== 'undefined' && localStorage ?
        localStorage.getItem('clara-rag-fast-mode') : null;
      const isRagFastMode = ragFastModeStr ? JSON.parse(ragFastModeStr) : false;

      if (documentsToProcess.length > 0) {
        if (isRagFastMode) {
          // ⚡ MODE RAPIDE : Pas de traitement RAG dans ChatManager
          console.log('⚡ MODE RAPIDE: Évitement du traitement RAG dans ChatManager (traitement direct dans ClaraAssistant)');
          // Le contexte RAG sera géré directement dans ClaraAssistant
        } else {
          // 🧠 MODE PERFORMANT : Traitement RAG complet
          console.log(`📚 MODE PERFORMANT: Traitement RAG total: ${documentsToProcess.length} documents`);

          const ragDocuments = RagManager.convertDocuments(documentsToProcess);
          // 🚀 ISOLATION: Obtenir le mode RAG de la session
          const sessionRagMode = context.sessionId ? getRagModeForSession(context.sessionId) : 'performant';
          const sessionConfig = { ragMode: sessionRagMode };

          const ragResult = await this.ragManager.processDocuments(
            ragDocuments,
            message,
            undefined, // onContentChunk
            context.sessionId, // 🚀 ISOLATION: Passer l'ID de session
            sessionConfig // 🚀 ISOLATION: Config de session avec mode RAG
          );

          context.ragContext = ragResult.ragContext;

          // 🚀 PERSISTANCE: Créer des messages RAG pour l'historique
          context.ragMessages = documentsToProcess.map((doc, index) => ({
            id: `rag-${Date.now()}-${index}`,
            role: 'system' as const,
            content: `📄 Document RAG: ${doc.name}\n\n${doc.content}`,
            timestamp: new Date().toISOString(),
            type: 'rag' as const,
            metadata: {
              documentId: doc.id,
              documentName: doc.name,
              documentType: doc.type || 'unknown',
              ragMode: sessionRagMode,
              processingStats: ragResult.processingStats
            }
          }));

          console.log('✅ Contexte RAG préparé:', ragResult.processingStats);
          console.log('📄 Messages RAG créés:', context.ragMessages?.length || 0);
        }
      } else {
        console.log('ℹ️ Aucun document disponible pour le RAG');
      }
    } catch (error) {
      console.warn('⚠️ Erreur traitement RAG:', error);
    }

    return context;
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Préparer les messages pour l'API
   */
  private async prepareMessages(context: ChatContext): Promise<ChatMessage[]> {
    const messages: ChatMessage[] = [];

    // Message système
    const systemContent = this.buildSystemPrompt(context);
    messages.push({
      role: 'system',
      content: systemContent
    });

    // 🧠 Historique de conversation avec gestion intelligente
    if (context.conversationHistory && context.conversationHistory.length > 0) {
      try {
        // Analyser si compression nécessaire avec le nouveau compresseur
        const { perfectCompressor } = await import('../context/PerfectCompressor');
        const shouldCompress = perfectCompressor.shouldCompress(context.conversationHistory);

        // 🚀 UTILISER L'HISTORIQUE DIRECTEMENT - Compression post-réponse
        const compressionStatus = shouldCompress ? 'Compression post-réponse nécessaire' : 'Pas de compression nécessaire';
        console.log(`ℹ️ ${compressionStatus}`);

        const historyMessages = context.conversationHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        }));
        messages.push(...historyMessages);

      } catch (error) {
        console.warn('⚠️ Erreur analyse compression, utilisation historique direct:', error);

        // Fallback : utilisation directe de l'historique
        const historyMessages = context.conversationHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        }));
        messages.push(...historyMessages);
      }
    }

    // Message utilisateur actuel
    messages.push({
      role: 'user',
      content: context.message
    });

    // 🔧 CORRECTION DUPLICATION : Vérifier et nettoyer les doublons dans les messages
    const uniqueMessages = this.removeDuplicateMessages(messages);

    // 🔍 DIAGNOSTIC DU CONTEXTE pour détecter les problèmes
    try {
      const { diagnoseContext } = await import('../../../utils/contextDiagnostic');
      const systemContent = uniqueMessages.find(m => m.role === 'system')?.content || '';
      const otherMessages = uniqueMessages.filter(m => m.role !== 'system');

      const diagnostic = diagnoseContext(systemContent, otherMessages);

      if (diagnostic.issues.length > 0) {
        console.warn('🚨 Problèmes de contexte détectés:', diagnostic.issues);
      }

      const qualityScore = diagnostic.estimatedTokens < 35000 ?
        Math.max(0, 100 - diagnostic.issues.length * 20) : 50;

      console.log(`🎯 Qualité du contexte: ${qualityScore}/100 (${diagnostic.estimatedTokens} tokens)`);
    } catch (error) {
      console.warn('⚠️ Erreur diagnostic contexte:', error);
    }

    console.log(`📝 ${uniqueMessages.length} messages préparés pour l'API (${messages.length - uniqueMessages.length} doublons supprimés)`);
    return uniqueMessages;
  }

  /**
   * 🔧 Supprimer les messages dupliqués
   */
  private removeDuplicateMessages(messages: any[]): any[] {
    const seen = new Set<string>();
    const uniqueMessages: any[] = [];

    for (const message of messages) {
      // Créer une clé unique basée sur le rôle et le contenu
      const key = `${message.role}:${message.content.substring(0, 100)}`;

      if (!seen.has(key)) {
        seen.add(key);
        uniqueMessages.push(message);
      } else {
        console.log(`🔧 Message dupliqué supprimé: ${message.role} - ${message.content.substring(0, 50)}...`);
      }
    }

    return uniqueMessages;
  }

  /**
   * Construire le prompt système - AVEC DOCUMENTS RAG DEPUIS LA BASE
   */
  private buildSystemPrompt(context: ChatContext): string {
    let systemPrompt = context.systemPrompt ||
      "Tu es WeMa IA, un assistant intelligent et professionnel. RÈGLES STRICTES: Réponds TOUJOURS en français sauf demande explicite contraire. Sois direct, précis et factuel. Si des documents sont fournis dans l'historique, utilise UNIQUEMENT ces informations. N'invente JAMAIS d'informations non présentes dans les documents. Si tu ne sais pas, dis 'Je ne trouve pas cette information dans les documents fournis'.";

    // 1. Ajouter le contexte RAG externe si disponible (mode performant) - SANS DUPLICATION
    if (context.ragContext) {
      systemPrompt += `\n\nINFO: Contexte RAG externe disponible (${context.ragContext.length} caractères).`;
    }

    // 2. 🚀 SUPPRESSION INJECTION RAG DANS SYSTEM PROMPT
    // Les documents RAG sont UNIQUEMENT dans l'historique comme messages système
    // AUCUNE mention dans le system prompt pour éviter la duplication
    if (context.conversationHistory) {
      const ragMessagesInHistory = context.conversationHistory.filter(msg =>
        msg.role === 'system' && msg.metadata?.type === 'rag'
      );

      // 🔍 DEBUG : Afficher TOUS les messages pour comprendre
      console.log(`🔍 DEBUG buildSystemPrompt: ${context.conversationHistory.length} messages total`);
      console.log(`🔍 DEBUG RAG messages trouvés:`, ragMessagesInHistory.map(msg => ({
        id: msg.id,
        role: msg.role,
        type: msg.metadata?.type,
        docName: msg.metadata?.documentName,
        contentLength: msg.content?.length
      })));

      if (ragMessagesInHistory.length > 0) {
        console.log(`🚀 ${ragMessagesInHistory.length} documents RAG dans l'historique (AUCUNE injection dans system prompt)`);
      }
    }

    return systemPrompt;
  }

  /**
   * Sélectionner le modèle approprié
   */
  private selectModel(context: ChatContext): string {
    let modelId = context.config.models.text || 'magistral:latest';

    // Si le modèle est vide ou undefined, utiliser magistral par défaut
    if (!modelId || modelId.trim() === '') {
      modelId = 'magistral:latest';
      console.log(`⚠️ Aucun modèle configuré, utilisation du modèle par défaut: ${modelId}`);
    }

    // Extraire le nom du modèle si préfixé par le provider
    if (modelId.includes(':')) {
      const parts = modelId.split(':');
      modelId = parts.slice(1).join(':');
    }

    console.log(`🤖 Modèle sélectionné: ${modelId}`);
    return modelId;
  }

  /**
   * Construire les options de génération
   */
  private buildGenerationOptions(config: ClaraAIConfig): any {
    return {
      temperature: config.parameters.temperature || 0.7,
      max_tokens: config.maxTokens || 4000,
      top_p: config.parameters.topP || 0.9,
      frequency_penalty: config.parameters.frequencyPenalty || 0,
      presence_penalty: config.parameters.presencePenalty || 0
    };
  }

  /**
   * Vérifier si le streaming avec outils est supporté
   */
  private supportsStreamingWithTools(): boolean {
    if (!this.currentProvider) return false;
    
    // Les providers locaux supportent généralement le streaming avec outils
    const baseUrl = this.currentProvider.baseUrl?.toLowerCase() || '';
    return baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1');
  }

  /**
   * Convertir les outils au format OpenAI
   */
  private convertToolsToOpenAIFormat(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object',
          properties: tool.parameters.reduce((props, param) => {
            props[param.name] = {
              type: param.type,
              description: param.description
            };
            return props;
          }, {} as any),
          required: tool.parameters.filter(p => p.required).map(p => p.name)
        }
      }
    }));
  }

  /**
   * Collecter les appels d'outils depuis le streaming
   */
  private collectToolCalls(chunkToolCalls: any[], collected: any[]): void {
    for (const toolCall of chunkToolCalls) {
      let existingCall = collected.find(c => c.id === toolCall.id);
      
      if (!existingCall) {
        existingCall = {
          id: toolCall.id || `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: toolCall.type || 'function',
          function: { name: '', arguments: '' }
        };
        collected.push(existingCall);
      }
      
      if (toolCall.function?.name) {
        existingCall.function.name = toolCall.function.name;
      }
      
      if (toolCall.function?.arguments) {
        existingCall.function.arguments += toolCall.function.arguments;
      }
    }
  }

  /**
   * 🧠 Extraire les documents de l'historique de conversation
   */
  private extractDocumentsFromHistory(conversationHistory: ClaraMessage[]): any[] {
    const documents: any[] = [];

    for (const message of conversationHistory) {
      // Chercher les mentions de documents dans les métadonnées
      if (message.metadata?.attachments) {
        documents.push(...message.metadata.attachments);
      }

      // Chercher les documents dans le contenu (patterns courants)
      if (message.role === 'user' && message.content) {
        const content = message.content.toLowerCase();

        // Détection de patterns indiquant un document
        const documentPatterns = [
          /voici.*doc/,
          /document.*joint/,
          /fichier.*attach/,
          /cv.*analys/,
          /document.*suivant/,
          /voici.*fichier/
        ];

        const hasDocumentPattern = documentPatterns.some(pattern => pattern.test(content));

        if (hasDocumentPattern) {
          // Créer un document virtuel basé sur le contexte
          const virtualDoc = {
            id: `history-doc-${message.id}`,
            filename: this.extractFilenameFromMessage(message.content),
            content: this.extractDocumentContentFromContext(message, conversationHistory),
            fileType: 'document',
            source: 'conversation-history'
          };

          if (virtualDoc.content && virtualDoc.content.length > 50) {
            // 🔧 ÉVITER L'ACCUMULATION : Vérifier si un document similaire existe déjà
            const existingDoc = documents.find(doc =>
              doc.filename === virtualDoc.filename ||
              (doc.content && doc.content.substring(0, 100) === virtualDoc.content.substring(0, 100))
            );

            if (!existingDoc) {
              documents.push(virtualDoc);
              console.log(`🔍 Document virtuel créé: ${virtualDoc.filename} (${virtualDoc.content.length} chars)`);
            } else {
              console.log(`⚠️ Document virtuel ignoré (déjà existant): ${virtualDoc.filename}`);
            }
          }
        }
      }
    }

    return documents;
  }

  /**
   * Extraire le nom de fichier probable d'un message
   */
  private extractFilenameFromMessage(content: string): string {
    // Chercher des patterns de noms de fichiers
    const filenamePatterns = [
      /([a-zA-Z0-9_-]+\.(pdf|doc|docx|txt|cv))/i,
      /(cv|resume|curriculum)/i
    ];

    for (const pattern of filenamePatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return 'document-from-conversation.txt';
  }

  /**
   * Extraire le contenu du document depuis le contexte de conversation
   */
  private extractDocumentContentFromContext(userMessage: ClaraMessage, history: ClaraMessage[]): string {
    // Chercher la réponse de l'assistant qui suit ce message
    const messageIndex = history.findIndex(m => m.id === userMessage.id);

    if (messageIndex >= 0 && messageIndex < history.length - 1) {
      const assistantResponse = history[messageIndex + 1];

      if (assistantResponse && assistantResponse.role === 'assistant') {
        const response = assistantResponse.content;

        // Si la réponse contient une analyse détaillée, c'est probablement basée sur un document
        if (response.length > 200 && (
          response.includes('CV') ||
          response.includes('profil') ||
          response.includes('expérience') ||
          response.includes('compétences') ||
          response.includes('formation')
        )) {
          // Extraire les informations structurées de la réponse
          return this.reconstructDocumentFromAnalysis(response);
        }
      }
    }

    return '';
  }

  /**
   * Reconstruire le contenu du document à partir de l'analyse de l'assistant
   */
  private reconstructDocumentFromAnalysis(analysis: string): string {
    // Extraire les sections principales de l'analyse
    const sections = [];

    // Chercher les sections structurées
    const sectionPatterns = [
      { name: 'PROFIL', pattern: /(?:profil|résumé|objectif)[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/is },
      { name: 'EXPÉRIENCE', pattern: /(?:expérience|emploi|poste)[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/is },
      { name: 'FORMATION', pattern: /(?:formation|éducation|diplôme)[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/is },
      { name: 'COMPÉTENCES', pattern: /(?:compétences|skills|technologies)[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/is }
    ];

    for (const section of sectionPatterns) {
      const match = analysis.match(section.pattern);
      if (match && match[1]) {
        sections.push(`${section.name}:\n${match[1].trim()}\n`);
      }
    }

    if (sections.length > 0) {
      return sections.join('\n');
    }

    // Fallback : utiliser l'analyse complète si elle est substantielle
    return analysis.length > 500 ? analysis : '';
  }

  /**
   * Créer un message Clara à partir de la réponse
   */
  private createClaraMessage(
    content: string,
    config: ClaraAIConfig,
    modelId: string,
    tokens: number,
    toolCalls?: any[]
  ): ClaraMessage {
    return {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      role: 'assistant',
      content: content || 'Je m\'excuse, mais je n\'ai pas pu générer de réponse.',
      timestamp: new Date(),
      metadata: {
        model: `${config.provider}:${modelId}`,
        tokens,
        temperature: config.parameters.temperature,
        toolsUsed: toolCalls?.map(tc => tc.function?.name).filter(Boolean) || [],
        autonomousMode: false
      }
    };
  }

  /**
   * 🔄 Compression post-réponse intelligente et asynchrone - ISOLÉE PAR SESSION
   */
  public async schedulePostResponseCompression(
    conversationHistory: ClaraMessage[],
    newMessage: ClaraMessage,
    compressionAnalysis?: any,
    sessionId?: string,
    ragContext?: string  // 🚀 NOUVEAU: Inclure le contexte RAG
  ): Promise<void> {
    try {
      // Ajouter le nouveau message à l'historique pour l'analyse
      const updatedHistory = [...conversationHistory, newMessage];

      // 🚀 NOUVEAU COMPRESSEUR PARFAIT
      const { perfectCompressor } = await import('../context/PerfectCompressor');
      const shouldCompress = perfectCompressor.shouldCompress(updatedHistory, ragContext, systemPrompt);

      // 🔧 LOG AVEC CALCUL IDENTIQUE AU PERFECTCOMPRESSOR
      const estimateTokens = (text: string): number => {
        if (!text) return 0;
        const words = text.split(/\s+/).filter(word => word.length > 0);
        const specialChars = (text.match(/[^\w\s]/g) || []).length;
        return Math.ceil(words.length * 1.3 + specialChars * 0.5);
      };

      const conversationMessages = updatedHistory.filter(msg => !(msg.role === 'system' && msg.metadata?.type === 'rag'));
      const conversationTokens = estimateTokens(conversationMessages.map(msg => msg.content || '').join(' '));
      const ragMessagesInHistory = updatedHistory.filter(msg => msg.role === 'system' && msg.metadata?.type === 'rag');
      const ragTokensFromHistory = estimateTokens(ragMessagesInHistory.map(msg => msg.content || '').join(' '));
      const ragTokensFromContext = ragContext ? estimateTokens(ragContext) : 0;
      const systemTokens = estimateTokens(systemPrompt);
      const totalRagTokens = ragTokensFromHistory + ragTokensFromContext;
      const totalTokens = conversationTokens + totalRagTokens + systemTokens;

      console.log(`🔍 Analyse post-réponse IDENTIQUE Context Manager: ${conversationTokens} tokens conversation + ${totalRagTokens} tokens RAG + ${systemTokens} tokens system = ${totalTokens} tokens total, ${updatedHistory.length} messages, shouldCompress: ${shouldCompress}`);

      if (shouldCompress) {
        console.log(`🧠 Démarrage compression intelligente: ${totalTokens} tokens, ${updatedHistory.length} messages`);

        // 🚀 COMPRESSION PARFAITE - SIMPLE ET EFFICACE (avec contexte RAG)
        perfectCompressor.compress(updatedHistory, {
          maxMessages: 15,
          preserveRecent: 3
        }, ragContext).then((compressedMessages) => {
          console.log(`✅ Compression réussie: ${updatedHistory.length} → ${compressedMessages.length} messages`);

          // Calculer les métriques
          const originalChars = updatedHistory.reduce((sum, msg) => sum + msg.content.length, 0);
          const compressedChars = compressedMessages.reduce((sum, msg) => sum + msg.content.length, 0);
          const ratio = originalChars / compressedChars;

          console.log(`📊 Compression: ${originalChars} → ${compressedChars} chars (${ratio.toFixed(2)}x)`);

          // Déclencher la mise à jour des messages dans l'interface
          this.notifyCompressionComplete(compressedMessages, sessionId || 'default');

        }).catch((error) => {
          console.error('❌ ERREUR COMPRESSION - ÉCHEC TOTAL:', error);

          // Pas de fallback - laisser l'erreur remonter
          throw error;
        });
      } else {
        console.log(`ℹ️ Compression non nécessaire`);
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors de la compression post-réponse:', error);
    }
  }

  /**
   * 🔄 Notifier la completion de compression pour mise à jour de l'interface
   */
  private notifyCompressionComplete(compressedMessages: ClaraMessage[], sessionId: string): void {
    // Émettre un événement personnalisé pour notifier l'interface
    const event = new CustomEvent('compression-complete', {
      detail: {
        compressedMessages,
        sessionId,
        timestamp: new Date()
      }
    });

    // Dispatcher l'événement sur window pour que les composants React puissent l'écouter
    window.dispatchEvent(event);

    console.log(`📡 Événement compression-complete émis pour session ${sessionId}`);
  }
}

// Export singleton
export const chatManager = new ChatManager();
