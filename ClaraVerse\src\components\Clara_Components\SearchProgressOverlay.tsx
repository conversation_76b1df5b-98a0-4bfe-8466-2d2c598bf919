/**
 * 🔍 Search Progress Overlay - Animation ÉPURÉE pour la recherche internet
 * Interface minimaliste et professionnelle
 */

import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';

interface SearchProgressOverlayProps {
  isVisible: boolean;
  query: string;
  onComplete?: () => void;
  realSources?: string[];
  searchResults?: any;
}

const SearchProgressOverlay: React.FC<SearchProgressOverlayProps> = ({
  isVisible,
  query,
  onComplete,
  realSources = [],
  searchResults
}) => {
  const [progress, setProgress] = useState(0);

  // Animation simple avec progression
  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      return;
    }

    // Animation progressive simple
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          // Fermer après un court délai
          setTimeout(() => {
            onComplete?.();
          }, 800);
          return 100;
        }
        return prev + 2; // Progression de 2% toutes les 100ms = 5 secondes total
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/10 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm w-full mx-4 animate-in zoom-in-95 duration-200">
        {/* Header épuré */}
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <Search className="w-4 h-4 text-white animate-pulse" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 dark:text-white text-sm">
              Recherche en cours...
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              "{query}"
            </p>
          </div>
        </div>

        {/* Barre de progression épurée */}
        <div className="space-y-2">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <div
              className="bg-blue-500 h-1.5 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Recherche internet</span>
            <span>{Math.round(progress)}%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchProgressOverlay;
