/**
 * 🚀 Configuration Serveur - Frontend/Backend séparés
 * Configuration pour déploiement client-serveur
 */

// Configuration par défaut (développement local)
const DEFAULT_CONFIG = {
  // Backend API - TOUJOURS LOCAL à l'application
  BACKEND_URL: 'http://localhost:5001',

  // Ollama serveur central - DÉLÉGATION D'INFÉRENCE VIA BACKEND
  OLLAMA_URL: 'http://*********:11434',

  // Timeouts adaptés pour réseau
  API_TIMEOUT: 45000,
  HEALTH_CHECK_INTERVAL: 90000,
  CONNECTION_RETRY_DELAY: 2000,
  MAX_RETRIES: 3,

  // Mode
  DEVELOPMENT_MODE: true,
  DEBUG_LOGS: true
};

// Configuration production (serveur distant)
const PRODUCTION_CONFIG = {
  // Backend API - TOUJOURS LOCAL à l'application (même en production)
  BACKEND_URL: 'http://localhost:5001',

  // Ollama serveur central - DÉLÉGATION D'INFÉRENCE VIA BACKEND
  OLLAMA_URL: 'http://*********:11434',

  // Timeouts plus longs pour réseau
  API_TIMEOUT: 60000,
  HEALTH_CHECK_INTERVAL: 120000,
  CONNECTION_RETRY_DELAY: 3000,
  MAX_RETRIES: 5,

  // Mode
  DEVELOPMENT_MODE: false,
  DEBUG_LOGS: false
};

// Détection automatique de l'environnement
const isProduction = import.meta.env.MODE === 'production';
const isServerMode = import.meta.env.VITE_SERVER_MODE === 'true';

// Configuration finale
export const SERVER_CONFIG = {
  ...(isProduction || isServerMode ? PRODUCTION_CONFIG : DEFAULT_CONFIG),

  // Override avec variables d'environnement
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL ||
               (isProduction || isServerMode ? PRODUCTION_CONFIG.BACKEND_URL : DEFAULT_CONFIG.BACKEND_URL),

  // Flags
  IS_PRODUCTION: isProduction,
  IS_SERVER_MODE: isServerMode,
  IS_LOCAL_MODE: !isProduction && !isServerMode
};

// Utilitaires
export const getBackendUrl = (endpoint: string = '') => {
  const baseUrl = SERVER_CONFIG.BACKEND_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.replace(/^\//, '');
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};



export const getOllamaUrl = (endpoint: string = '') => {
  const baseUrl = SERVER_CONFIG.OLLAMA_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.replace(/^\//, '');
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};

// Validation de configuration
export const validateServerConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!SERVER_CONFIG.BACKEND_URL) {
    errors.push('BACKEND_URL manquant');
  }



  if (!SERVER_CONFIG.OLLAMA_URL) {
    errors.push('OLLAMA_URL manquant');
  }

  try {
    new URL(SERVER_CONFIG.BACKEND_URL);
  } catch {
    errors.push('BACKEND_URL invalide');
  }



  try {
    new URL(SERVER_CONFIG.OLLAMA_URL);
  } catch {
    errors.push('OLLAMA_URL invalide');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// Test de connectivité
export const testServerConnectivity = async (): Promise<{
  backend: boolean;
  ollama: boolean;
  errors: string[];
}> => {
  const errors: string[] = [];
  let backend = false;
  let ollama = false;

  // Test backend local
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(getBackendUrl('/health'), {
      method: 'GET',
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    backend = response.ok;
  } catch (error) {
    errors.push(`Backend local inaccessible: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }



  // Test Ollama serveur central (via backend proxy)
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // Plus de temps pour réseau

    // 🚀 ARCHITECTURE SÉCURISÉE : Tester via le backend proxy
    const response = await fetch(getBackendUrl('/proxy/ollama/models'), {
      method: 'GET',
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    ollama = response.ok;
  } catch (error) {
    errors.push(`Ollama serveur central inaccessible via backend: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }

  return { backend, ollama, errors };
};

// Log de configuration au démarrage
if (SERVER_CONFIG.DEBUG_LOGS) {
  console.log('🚀 Configuration serveur WeMa IA:', {
    mode: SERVER_CONFIG.IS_PRODUCTION ? 'PRODUCTION' :
          SERVER_CONFIG.IS_SERVER_MODE ? 'SERVER' : 'LOCAL',
    backend_local: SERVER_CONFIG.BACKEND_URL,
    ollama_central: SERVER_CONFIG.OLLAMA_URL,
    timeout: SERVER_CONFIG.API_TIMEOUT,
    architecture: 'Tout via Backend - Sécurisé'
  });
}
