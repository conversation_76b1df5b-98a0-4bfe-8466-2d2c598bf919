/**
 * 🌐 Internet Search Tool - Tool Use Integration
 * 
 * Responsabilités :
 * - Définition du tool search_internet pour les LLMs
 * - Exécution des recherches via internetSearchService
 * - Formatage des résultats pour les LLMs
 * - Support Ollama + LM Studio via format OpenAI
 */

import internetSearchService from '../../internetSearchService';
import type { Tool } from '../../../db';
import { logger, LogCategory } from '../../../utils/logger';

/**
 * Configuration du tool search_internet
 */
export const INTERNET_SEARCH_TOOL: Tool = {
  id: 'search_internet',
  name: 'search_internet',
  description: 'Search the internet for current, accurate information. Use this when you need recent data, technical specifications, news, or any information that might have changed recently.',
  parameters: [
    {
      name: 'query',
      type: 'string',
      description: 'Search query optimized to find relevant information. Be specific and use relevant keywords.',
      required: true
    },
    {
      name: 'search_type',
      type: 'string',
      description: 'Type of search to perform: general, technical, news, shopping',
      required: false
    }
  ],
  implementation: 'internet_search',
  isEnabled: true
};

/**
 * Résultat d'une recherche internet pour tool use
 */
export interface InternetSearchResult {
  success: boolean;
  content?: string;
  error?: string;
  sources?: string[];
  searchTime?: number;
}

/**
 * Gestionnaire du tool search_internet
 */
export class InternetSearchToolManager {
  
  /**
   * Exécuter une recherche internet via tool call
   */
  public async executeSearch(args: {
    query: string;
    search_type?: 'general' | 'technical' | 'news' | 'shopping';
  }): Promise<InternetSearchResult> {
    const startTime = Date.now();
    
    try {
      logger.info(LogCategory.TOOLS, `🔍 Tool search_internet: "${args.query}"`);
      
      // Validation des arguments
      if (!args.query || args.query.trim().length === 0) {
        return {
          success: false,
          error: 'Query parameter is required and cannot be empty'
        };
      }

      // Configuration de la recherche selon le type
      const searchOptions = this.getSearchOptions(args.search_type || 'general');
      
      // Exécuter la recherche
      const response = await internetSearchService.search(args.query.trim(), searchOptions);
      
      const searchTime = Date.now() - startTime;
      
      if (!response.results || response.results.length === 0) {
        return {
          success: false,
          error: 'No search results found',
          searchTime
        };
      }

      // Utiliser le contenu formaté du backend si disponible
      const formattedContent = (response as any).formattedResult || this.formatSearchResults(response);
      const sources = response.results.map(r => r.url);
      
      logger.info(LogCategory.TOOLS, `✅ Search completed: ${response.results.length} results in ${searchTime}ms`);
      
      return {
        success: true,
        content: formattedContent,
        sources,
        searchTime
      };
      
    } catch (error) {
      const searchTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown search error';
      
      logger.error(LogCategory.TOOLS, `❌ Search failed: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
        searchTime
      };
    }
  }

  /**
   * Obtenir les options de recherche selon le type
   */
  private getSearchOptions(searchType: string) {
    switch (searchType) {
      case 'technical':
        return {
          categories: ['general', 'it'],
          maxResults: 6,
          timeout: 25000
        };
      case 'news':
        return {
          categories: ['news'],
          maxResults: 8,
          timeout: 20000
        };
      case 'shopping':
        return {
          categories: ['shopping'],
          maxResults: 5,
          timeout: 20000
        };
      default: // 'general'
        return {
          categories: ['general'],
          maxResults: 8,
          timeout: 25000
        };
    }
  }

  /**
   * Formater les résultats de recherche pour le LLM
   */
  private formatSearchResults(response: any): string {
    if (!response.results || response.results.length === 0) {
      return 'Aucun résultat trouvé.';
    }

    let formatted = `🔍 **Résultats de recherche pour: "${response.query}"**\n\n`;

    response.results.forEach((result: any, index: number) => {
      formatted += `**${index + 1}. ${result.title}**\n`;
      formatted += `Source: ${result.url}\n`;

      if (result.content && result.content.trim()) {
        // Limiter le contenu pour éviter des contextes trop volumineux
        const content = result.content.trim();
        const truncatedContent = content.length > 500
          ? content.substring(0, 500) + '...'
          : content;
        formatted += `${truncatedContent}\n\n`;
      } else if (result.snippet) {
        formatted += `${result.snippet}\n\n`;
      }
    });

    return formatted;
  }

  /**
   * Obtenir le tool au format OpenAI pour les providers
   */
  public getOpenAIToolFormat() {
    return {
      type: 'function',
      function: {
        name: INTERNET_SEARCH_TOOL.name,
        description: INTERNET_SEARCH_TOOL.description,
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'Search query optimized to find relevant information. Be specific and use relevant keywords.'
            },
            search_type: {
              type: 'string',
              enum: ['general', 'technical', 'news', 'shopping'],
              description: 'Type of search to perform',
              default: 'general'
            }
          },
          required: ['query']
        }
      }
    };
  }
}

// Instance singleton
export const internetSearchToolManager = new InternetSearchToolManager();
