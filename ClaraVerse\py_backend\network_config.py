"""
🌐 CONFIGURATION RÉSEAU MULTI-ENVIRONNEMENT - WeMa IA
Support automatique pour réseau bureau et partage de connexion
"""

import requests
import logging
from typing import List, Optional, Dict, Any
import time

logger = logging.getLogger(__name__)

class NetworkConfig:
    """Gestionnaire de configuration réseau intelligent"""
    
    def __init__(self):
        # 🌐 ADRESSES POSSIBLES DU SERVEUR OLLAMA
        self.possible_servers = [
            {
                "url": "http://172.20.10.2:11434",
                "name": "Partage de connexion téléphone",
                "type": "hotspot",
                "priority": 1  # Priorité haute (plus récent)
            },
            {
                "url": "http://10.4.123.77:11434",
                "name": "Réseau bureau (nouveau)",
                "type": "office",
                "priority": 2  # Priorité normale
            },
            {
                "url": "http://10.0.0.17:11434",
                "name": "Réseau bureau (ancien)",
                "type": "office_legacy",
                "priority": 3  # Priorité basse (fallback)
            }
        ]
        
        self.current_server = None
        self.last_check_time = 0
        self.check_interval = 30  # Vérifier toutes les 30 secondes
        
    def find_working_server(self, timeout: int = 1) -> Optional[Dict[str, Any]]:
        """Trouver le serveur Ollama qui fonctionne"""
        current_time = time.time()

        # Éviter les vérifications trop fréquentes
        if (self.current_server and
            current_time - self.last_check_time < self.check_interval):
            return self.current_server

        logger.info("🔍 Recherche du serveur Ollama disponible...")

        # Trier par priorité (1 = plus haute priorité)
        servers_by_priority = sorted(self.possible_servers, key=lambda x: x["priority"])

        for server in servers_by_priority:
            if self._test_server(server["url"], timeout):
                logger.info(f"✅ Serveur trouvé: {server['name']} ({server['url']})")
                self.current_server = server
                self.last_check_time = current_time
                return server

        logger.warning("⚠️ Aucun serveur Ollama accessible")
        # Marquer qu'aucun serveur n'est disponible pour éviter les re-tentatives fréquentes
        self.current_server = None
        self.last_check_time = current_time
        return None
    
    def _test_server(self, url: str, timeout: int) -> bool:
        """Tester si un serveur Ollama est accessible"""
        try:
            logger.debug(f"🔍 Test: {url}")
            # Timeout très court pour éviter les blocages
            response = requests.get(f"{url}/api/tags", timeout=timeout)

            if response.status_code == 200:
                # Vérifier que c'est bien Ollama
                try:
                    data = response.json()
                    if "models" in data or isinstance(data, dict):
                        return True
                except:
                    pass

            logger.debug(f"❌ {url} - Status: {response.status_code}")
            return False

        except requests.exceptions.ConnectTimeout:
            logger.debug(f"❌ {url} - Timeout de connexion")
            return False
        except requests.exceptions.ConnectionError:
            logger.debug(f"❌ {url} - Erreur de connexion")
            return False
        except Exception as e:
            logger.debug(f"❌ {url} - Erreur: {e}")
            return False
    
    def get_ollama_url(self) -> str:
        """Obtenir l'URL du serveur Ollama actuel"""
        server = self.find_working_server()
        if server:
            return server["url"]

        # Fallback sur l'adresse bureau par défaut
        logger.warning("⚠️ Utilisation de l'adresse par défaut (bureau)")
        return "http://10.0.0.17:11434"

    def is_ollama_available(self) -> bool:
        """Vérifier rapidement si Ollama est disponible"""
        server = self.find_working_server(timeout=1)
        return server is not None
    
    def get_server_info(self) -> Dict[str, Any]:
        """Obtenir les informations du serveur actuel"""
        server = self.find_working_server()
        if server:
            return {
                "url": server["url"],
                "name": server["name"],
                "type": server["type"],
                "status": "connected",
                "last_check": self.last_check_time
            }
        
        return {
            "url": "http://10.0.0.17:11434",
            "name": "Réseau bureau (par défaut)",
            "type": "office",
            "status": "unknown",
            "last_check": self.last_check_time
        }
    
    def force_refresh(self):
        """Forcer une nouvelle vérification des serveurs"""
        self.last_check_time = 0
        self.current_server = None
        return self.find_working_server()
    
    def add_server(self, url: str, name: str, server_type: str = "custom", priority: int = 3):
        """Ajouter un nouveau serveur à tester"""
        self.possible_servers.append({
            "url": url,
            "name": name,
            "type": server_type,
            "priority": priority
        })
        logger.info(f"➕ Serveur ajouté: {name} ({url})")
    
    def get_network_diagnostics(self) -> Dict[str, Any]:
        """Obtenir un diagnostic complet du réseau"""
        diagnostics = {
            "timestamp": time.time(),
            "current_server": self.get_server_info(),
            "all_servers": [],
            "recommendations": []
        }
        
        # Tester tous les serveurs
        for server in self.possible_servers:
            is_working = self._test_server(server["url"], timeout=2)
            server_info = {
                **server,
                "status": "online" if is_working else "offline",
                "response_time": None  # TODO: Mesurer le temps de réponse
            }
            diagnostics["all_servers"].append(server_info)
        
        # Générer des recommandations
        online_servers = [s for s in diagnostics["all_servers"] if s["status"] == "online"]
        
        if len(online_servers) == 0:
            diagnostics["recommendations"].append("❌ Aucun serveur accessible - Vérifier la configuration réseau")
        elif len(online_servers) == 1:
            diagnostics["recommendations"].append(f"✅ Un serveur disponible: {online_servers[0]['name']}")
        else:
            diagnostics["recommendations"].append(f"✅ {len(online_servers)} serveurs disponibles")
            
            # Recommander le serveur avec la plus haute priorité
            best_server = min(online_servers, key=lambda x: x["priority"])
            if best_server != diagnostics["current_server"]:
                diagnostics["recommendations"].append(f"💡 Serveur recommandé: {best_server['name']}")
        
        return diagnostics

# Instance globale
network_config = NetworkConfig()

def get_ollama_url() -> str:
    """Fonction utilitaire pour obtenir l'URL Ollama"""
    return network_config.get_ollama_url()

def get_server_info() -> Dict[str, Any]:
    """Fonction utilitaire pour obtenir les infos serveur"""
    return network_config.get_server_info()

def refresh_network() -> Dict[str, Any]:
    """Fonction utilitaire pour rafraîchir la configuration réseau"""
    network_config.force_refresh()
    return network_config.get_server_info()

def network_diagnostics() -> Dict[str, Any]:
    """Fonction utilitaire pour le diagnostic réseau"""
    return network_config.get_network_diagnostics()

# 🧪 FONCTION DE TEST
def test_network_config():
    """Tester la configuration réseau"""
    print("🧪 Test de la configuration réseau WeMa IA")
    print("=" * 50)
    
    # Test de base
    url = get_ollama_url()
    print(f"🌐 URL Ollama détectée: {url}")
    
    # Informations serveur
    info = get_server_info()
    print(f"📊 Serveur actuel: {info['name']} ({info['status']})")
    
    # Diagnostic complet
    diag = network_diagnostics()
    print(f"\n📋 Diagnostic complet:")
    for server in diag["all_servers"]:
        status_icon = "✅" if server["status"] == "online" else "❌"
        print(f"  {status_icon} {server['name']}: {server['url']} ({server['status']})")
    
    print(f"\n💡 Recommandations:")
    for rec in diag["recommendations"]:
        print(f"  {rec}")

if __name__ == "__main__":
    test_network_config()
