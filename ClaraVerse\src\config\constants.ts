/**
 * 🎯 CONFIGURATION UNIFIÉE - WeMa IA
 * 
 * Tous les seuils, limites et constantes de l'application centralisés
 * pour éviter les incohérences et faciliter la maintenance
 */

// ========================================
// COMPRESSION ET CONTEXTE
// ========================================

export const COMPRESSION_CONFIG = {
  // Seuil de déclenchement optimal - 15K pour tests
  TRIGGER_THRESHOLD: 15000, // 15K caractères - seuil plus bas pour déclenchement précoce

  // 🚀 ARCHITECTURE ACTUELLE: Serveur central Ollama
  MODEL: 'qwen3-14b-optimized:latest', // Modèle Qwen3 14B sur serveur central
  FALLBACK_MODEL: 'qwen3-30b-a3b-optimized:latest', // Fallback 30B disponible

  // 🚀 SERVEUR CENTRAL: Timeouts adaptés
  MAX_PROCESSING_TIME: 45000, // 45 secondes pour serveur central
  FALLBACK_TIMEOUT: 15000,     // 15 secondes pour fallback

  // Ratios de compression optimisés
  URGENCY_RATIOS: {
    IMMEDIATE: 0.5,  // Réduire à 50% si > 85% capacité
    BACKGROUND: 0.6, // Réduire à 60% si > 70% capacité
    PREVENTIVE: 0.7  // Réduction modérée si > seuil
  },

  // Seuils d'urgence adaptés au contexte étendu
  URGENCY_THRESHOLDS: {
    IMMEDIATE: 0.85, // > 85% = compression immédiate
    BACKGROUND: 0.7  // > 70% = compression en arrière-plan
  },

  // 🚀 CONFIGURATION SERVEUR CENTRAL OLLAMA
  CENTRAL_SERVER: {
    BASE_URL: 'http://localhost:5001/proxy/ollama', // Via notre backend proxy
    ENDPOINT: '/chat',
    TIMEOUT: 30000, // 30 secondes via proxy
    RETRY_ATTEMPTS: 2,
    RETRY_DELAY: 2000
  }
} as const;

// ========================================
// SYSTÈME RAG
// ========================================

export const RAG_CONFIG = {
  // Seuils par mode
  FAST_MODE: {
    THRESHOLD: 50000,        // 50K chars - favorise l'utilisation directe
    MAX_CONTEXT: 200000,     // 200K chars max
    ENABLE_CONTEXT_MGMT: false
  },
  
  PERFORMANT_MODE: {
    THRESHOLD: 20000,        // 20K chars - analyse hybride plus fine
    MAX_CONTEXT: 100000,     // 100K chars max
    ENABLE_CONTEXT_MGMT: true
  },
  
  // Configuration backend
  BACKEND: {
    CHUNK_SIZE: 1000,
    CHUNK_OVERLAP: 200,
    MAX_TOKENS: 4096,
    EMBEDDING_MODEL: 'BAAI/bge-m3'
  },
  
  // Performance
  PERFORMANCE: {
    BATCH_SIZE: 32,
    MAX_CONCURRENT: 4,
    CACHE_ENABLED: true,
    PRELOAD_EMBEDDINGS: true
  }
} as const;

// ========================================
// CONTEXTE ET TOKENS
// ========================================

export const CONTEXT_CONFIG = {
  // Limites par défaut
  DEFAULT_LIMITS: {
    MAX_TOKENS: 100000,      // 100K tokens par défaut
    RESERVE_TOKENS: 2000,    // 2K tokens réservés pour la réponse
    CHARS_PER_TOKEN: 4       // ~4 caractères par token
  },
  
  // Limites par type de modèle
  MODEL_LIMITS: {
    SMALL: {      // < 8B paramètres
      MAX_TOKENS: 32000,
      RESERVE_TOKENS: 1000,
      CHARS_PER_TOKEN: 4
    },
    MEDIUM: {     // 8B-32B paramètres
      MAX_TOKENS: 100000,
      RESERVE_TOKENS: 2000,
      CHARS_PER_TOKEN: 4
    },
    LARGE: {      // > 32B paramètres
      MAX_TOKENS: 200000,
      RESERVE_TOKENS: 4000,
      CHARS_PER_TOKEN: 4
    }
  },
  
  // Optimisation historique
  HISTORY_OPTIMIZATION: {
    MIN_MESSAGES: 5,         // Garder au minimum 5 messages
    PRESERVE_RECENT: 10,     // Toujours préserver les 10 derniers
    SUMMARY_MAX_LENGTH: 500  // Résumé max 500 chars
  }
} as const;

// ========================================
// PERFORMANCE ET CACHE
// ========================================

export const PERFORMANCE_CONFIG = {
  // Cache des providers
  HEALTH_CHECK_CACHE_TIME: 300000, // 5 minutes
  
  // Refresh et cooldowns
  REFRESH_COOLDOWN: 30000,         // 30 secondes
  
  // Timeouts
  DEFAULT_TIMEOUT: 30000,          // 30 secondes
  HEALTH_CHECK_TIMEOUT: 10000,     // 10 secondes
  
  // Concurrence
  MAX_CONCURRENCY: 10,
  
  // Logs groupés
  LOG_GROUP_TIMEOUT: 5000,         // 5 secondes
  MAX_GROUP_SIZE: 5                // 5 logs max par groupe
} as const;

// ========================================
// DOCUMENTS ET FICHIERS
// ========================================

export const DOCUMENT_CONFIG = {
  // Limites de sélection
  MAX_SELECTED_DOCUMENTS: 5,
  
  // Tailles de fichiers
  MAX_FILE_SIZE: 25 * 1024 * 1024, // 25MB
  
  // OCR
  OCR: {
    DPI: 200,
    LANGUAGE: 'fra',
    PRESERVE_LAYOUT: true,
    TABLE_DETECTION: true,
    CONFIDENCE_THRESHOLD: 0.7
  },
  
  // Types supportés
  SUPPORTED_TYPES: [
    'application/pdf',
    'text/plain',
    'text/markdown',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/tiff'
  ]
} as const;

// ========================================
// INTERFACE UTILISATEUR
// ========================================

export const UI_CONFIG = {
  // Notifications
  NOTIFICATION_DURATIONS: {
    INFO: 3000,
    SUCCESS: 2000,
    WARNING: 5000,
    ERROR: 8000,
    COMPLETION: 4000
  },
  
  // Animations
  ANIMATION_DURATION: 300,
  
  // Pagination
  SESSIONS_PER_PAGE: 20,
  
  // Auto-save
  AUTO_SAVE_INTERVAL: 30000, // 30 secondes
  
  // Thème
  THEME: {
    PRIMARY: '#60a5fa',      // wema-400
    PRIMARY_DARK: '#3b82f6', // wema-500
    PRIMARY_LIGHT: '#93c5fd' // wema-300
  }
} as const;

// ========================================
// DÉVELOPPEMENT ET DEBUG
// ========================================

export const DEBUG_CONFIG = {
  // Niveaux de log par environnement
  LOG_LEVEL: {
    DEVELOPMENT: 'DEBUG',
    PRODUCTION: 'WARN'
  },
  
  // Catégories activées par défaut
  DEFAULT_LOG_CATEGORIES: [
    'SYSTEM',
    'RAG',
    'PROVIDERS'
  ],
  
  // Fonctions debug exposées en développement
  EXPOSE_DEBUG_FUNCTIONS: true,
  
  // Métriques de performance
  ENABLE_PERFORMANCE_METRICS: true
} as const;

// ========================================
// VALIDATION ET HELPERS
// ========================================

/**
 * Obtenir la configuration de contexte pour un modèle
 */
export function getContextConfigForModel(modelSize: 'small' | 'medium' | 'large') {
  return CONTEXT_CONFIG.MODEL_LIMITS[modelSize.toUpperCase() as keyof typeof CONTEXT_CONFIG.MODEL_LIMITS];
}

/**
 * Obtenir la configuration RAG selon le mode
 */
export function getRagConfigForMode(fastMode: boolean) {
  return fastMode ? RAG_CONFIG.FAST_MODE : RAG_CONFIG.PERFORMANT_MODE;
}

/**
 * Calculer la limite de caractères pour un modèle
 */
export function getMaxCharsForModel(maxTokens: number, reserveTokens: number = CONTEXT_CONFIG.DEFAULT_LIMITS.RESERVE_TOKENS) {
  return (maxTokens - reserveTokens) * CONTEXT_CONFIG.DEFAULT_LIMITS.CHARS_PER_TOKEN;
}

/**
 * Vérifier si la compression est nécessaire
 */
export function shouldCompress(contentLength: number, modelContextWindow: number = CONTEXT_CONFIG.DEFAULT_LIMITS.MAX_TOKENS) {
  if (contentLength < COMPRESSION_CONFIG.TRIGGER_THRESHOLD) {
    return { shouldCompress: false, urgency: 'none' as const };
  }
  
  const modelLimitChars = modelContextWindow * CONTEXT_CONFIG.DEFAULT_LIMITS.CHARS_PER_TOKEN;
  const usageRatio = contentLength / modelLimitChars;
  
  if (usageRatio > COMPRESSION_CONFIG.URGENCY_THRESHOLDS.IMMEDIATE) {
    return { shouldCompress: true, urgency: 'immediate' as const };
  } else if (usageRatio > COMPRESSION_CONFIG.URGENCY_THRESHOLDS.BACKGROUND) {
    return { shouldCompress: true, urgency: 'background' as const };
  } else {
    return { shouldCompress: true, urgency: 'preventive' as const };
  }
}

// Export de toutes les configurations
export const CONFIG = {
  COMPRESSION: COMPRESSION_CONFIG,
  RAG: RAG_CONFIG,
  CONTEXT: CONTEXT_CONFIG,
  PERFORMANCE: PERFORMANCE_CONFIG,
  DOCUMENT: DOCUMENT_CONFIG,
  UI: UI_CONFIG,
  DEBUG: DEBUG_CONFIG
} as const;
