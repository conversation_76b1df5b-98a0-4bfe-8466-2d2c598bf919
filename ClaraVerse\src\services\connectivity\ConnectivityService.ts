/**
 * 🌐 Service de Test de Connectivité WeMa IA
 * 
 * Teste la connectivité vers :
 * - Backend local (toujours localhost:5001)
 * - Serveur Ollama central (*********:11434) - délégation d'inférence
 * - LM Studio local (fallback)
 */

import { getBackendUrl, getOllamaUrl, getLMStudioUrl, testServerConnectivity } from '../../config/server.config';

export interface ConnectivityStatus {
  backend: {
    available: boolean;
    url: string;
    responseTime?: number;
    error?: string;
  };
  ollama: {
    available: boolean;
    url: string;
    responseTime?: number;
    models?: string[];
    error?: string;
  };
  lmstudio: {
    available: boolean;
    url: string;
    responseTime?: number;
    models?: string[];
    error?: string;
  };
  overall: {
    status: 'healthy' | 'degraded' | 'critical';
    message: string;
  };
}

export class ConnectivityService {
  private static instance: ConnectivityService;
  private lastCheck: number = 0;
  private checkInterval: number = 60000; // 1 minute
  private cachedStatus: ConnectivityStatus | null = null;

  static getInstance(): ConnectivityService {
    if (!ConnectivityService.instance) {
      ConnectivityService.instance = new ConnectivityService();
    }
    return ConnectivityService.instance;
  }

  /**
   * Test complet de connectivité avec cache intelligent
   */
  async checkConnectivity(forceRefresh = false): Promise<ConnectivityStatus> {
    const now = Date.now();
    
    // Utiliser le cache si récent et pas de force refresh
    if (!forceRefresh && this.cachedStatus && (now - this.lastCheck) < this.checkInterval) {
      console.log('🎯 Using cached connectivity status');
      return this.cachedStatus;
    }

    console.log('🔍 Checking connectivity to all services...');
    const startTime = Date.now();

    // Test backend local
    const backendStatus = await this.testBackend();
    
    // Test Ollama serveur central (délégation d'inférence)
    const ollamaStatus = await this.testOllama();
    
    // Test LM Studio local (fallback)
    const lmstudioStatus = await this.testLMStudio();

    // Déterminer le statut global
    const overall = this.determineOverallStatus(backendStatus, ollamaStatus, lmstudioStatus);

    const status: ConnectivityStatus = {
      backend: backendStatus,
      ollama: ollamaStatus,
      lmstudio: lmstudioStatus,
      overall
    };

    // Mettre en cache
    this.cachedStatus = status;
    this.lastCheck = now;

    const totalTime = Date.now() - startTime;
    console.log(`✅ Connectivity check completed in ${totalTime}ms:`, {
      backend: backendStatus.available,
      ollama: ollamaStatus.available,
      lmstudio: lmstudioStatus.available,
      overall: overall.status
    });

    return status;
  }

  /**
   * Test du backend local (natif à l'application)
   */
  private async testBackend(): Promise<ConnectivityStatus['backend']> {
    const url = getBackendUrl('/health');
    const startTime = Date.now();

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          available: true,
          url,
          responseTime
        };
      } else {
        return {
          available: false,
          url,
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        available: false,
        url,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test du serveur Ollama central (délégation d'inférence uniquement)
   */
  private async testOllama(): Promise<ConnectivityStatus['ollama']> {
    const url = getOllamaUrl('/api/tags');
    const startTime = Date.now();

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // Plus de temps pour réseau

      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        const models = data.models?.map((m: any) => m.name) || [];
        
        return {
          available: true,
          url: getOllamaUrl(),
          responseTime,
          models
        };
      } else {
        return {
          available: false,
          url: getOllamaUrl(),
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        available: false,
        url: getOllamaUrl(),
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test de LM Studio local (fallback)
   */
  private async testLMStudio(): Promise<ConnectivityStatus['lmstudio']> {
    const url = getLMStudioUrl('/v1/models');
    const startTime = Date.now();

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);

      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        const models = data.data?.map((m: any) => m.id) || [];
        
        return {
          available: true,
          url: getLMStudioUrl(),
          responseTime,
          models
        };
      } else {
        return {
          available: false,
          url: getLMStudioUrl(),
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        available: false,
        url: getLMStudioUrl(),
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Déterminer le statut global du système
   */
  private determineOverallStatus(
    backend: ConnectivityStatus['backend'],
    ollama: ConnectivityStatus['ollama'],
    lmstudio: ConnectivityStatus['lmstudio']
  ): ConnectivityStatus['overall'] {
    
    // Backend local obligatoire
    if (!backend.available) {
      return {
        status: 'critical',
        message: 'Backend local indisponible - Application non fonctionnelle'
      };
    }

    // Ollama central préféré pour l'inférence
    if (ollama.available) {
      return {
        status: 'healthy',
        message: 'Tous les services fonctionnels - Inférence via serveur central'
      };
    }

    // Fallback LM Studio local
    if (lmstudio.available) {
      return {
        status: 'degraded',
        message: 'Serveur central indisponible - Fallback LM Studio local actif'
      };
    }

    // Aucune inférence disponible
    return {
      status: 'critical',
      message: 'Aucun service d\'inférence IA disponible'
    };
  }

  /**
   * Invalider le cache pour forcer un nouveau test
   */
  invalidateCache(): void {
    this.cachedStatus = null;
    this.lastCheck = 0;
  }

  /**
   * Obtenir le statut en cache (peut être null)
   */
  getCachedStatus(): ConnectivityStatus | null {
    return this.cachedStatus;
  }
}

// Export de l'instance singleton
export const connectivityService = ConnectivityService.getInstance();
