/**
 * 🔍 Configuration Validator - WeMa IA
 * Validation complète de l'architecture sécurisée
 */

import { SERVER_CONFIG, testServerConnectivity } from './server.config';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  architecture: string;
  securityLevel: 'HIGH' | 'MEDIUM' | 'LOW';
}

/**
 * 🔍 Valider la configuration complète
 */
export const validateConfiguration = async (): Promise<ValidationResult> => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 1. Validation de l'architecture
  console.log('🔍 Validation de l\'architecture WeMa IA...');
  
  // Vérifier que tout passe par le backend
  if (!SERVER_CONFIG.BACKEND_URL.includes('localhost:5001')) {
    errors.push('Backend URL doit pointer vers localhost:5001');
  }
  
  // Vérifier que l'URL Ollama est correcte
  if (!SERVER_CONFIG.OLLAMA_URL.includes('*********:11434')) {
    errors.push('Ollama URL doit pointer vers *********:11434');
  }
  
  // 2. Test de connectivité
  console.log('🔍 Test de connectivité...');
  const connectivity = await testServerConnectivity();
  
  if (!connectivity.backend) {
    errors.push('Backend local inaccessible');
  }
  
  if (!connectivity.ollama) {
    warnings.push('Serveur Ollama central inaccessible (normal si pas encore démarré)');
  }
  
  // Ajouter les erreurs de connectivité
  errors.push(...connectivity.errors.filter(e => e.includes('Backend')));
  warnings.push(...connectivity.errors.filter(e => e.includes('Ollama')));
  
  // 3. Validation de l'architecture sécurisée
  const architecture = 'Frontend → Backend Local → Serveur Central';
  const securityLevel: 'HIGH' | 'MEDIUM' | 'LOW' = errors.length === 0 ? 'HIGH' : 
                                                   warnings.length === 0 ? 'MEDIUM' : 'LOW';
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    architecture,
    securityLevel
  };
};

/**
 * 🔍 Valider les providers configurés
 */
export const validateProviders = async (): Promise<{
  validProviders: string[];
  invalidProviders: string[];
  recommendations: string[];
}> => {
  const validProviders: string[] = [];
  const invalidProviders: string[] = [];
  const recommendations: string[] = [];
  
  // Vérifier que seuls les providers supportés sont utilisés
  const supportedProviders = ['ollama', 'openai', 'openrouter', 'claras-pocket', 'custom'];
  
  // Recommandations de sécurité
  recommendations.push('✅ Utiliser uniquement Ollama pour l\'inférence centrale');
  recommendations.push('✅ Toutes les requêtes passent par le backend local');
  recommendations.push('✅ Pas de CORS direct vers les serveurs externes');
  recommendations.push('✅ Configuration centralisée et sécurisée');
  
  return {
    validProviders: supportedProviders,
    invalidProviders: [], // Plus de LM Studio
    recommendations
  };
};

/**
 * 🔍 Rapport de configuration complet
 */
export const generateConfigReport = async (): Promise<string> => {
  const validation = await validateConfiguration();
  const providers = await validateProviders();
  
  let report = `
🚀 RAPPORT DE CONFIGURATION WEMA IA
=====================================

📊 ARCHITECTURE:
${validation.architecture}

🔒 NIVEAU DE SÉCURITÉ: ${validation.securityLevel}

⚙️ CONFIGURATION:
- Backend Local: ${SERVER_CONFIG.BACKEND_URL}
- Serveur Central: ${SERVER_CONFIG.OLLAMA_URL}
- Mode: ${SERVER_CONFIG.IS_PRODUCTION ? 'PRODUCTION' : 'DÉVELOPPEMENT'}
- Timeout: ${SERVER_CONFIG.API_TIMEOUT}ms

`;

  if (validation.errors.length > 0) {
    report += `❌ ERREURS:\n`;
    validation.errors.forEach(error => {
      report += `  - ${error}\n`;
    });
    report += `\n`;
  }

  if (validation.warnings.length > 0) {
    report += `⚠️ AVERTISSEMENTS:\n`;
    validation.warnings.forEach(warning => {
      report += `  - ${warning}\n`;
    });
    report += `\n`;
  }

  report += `✅ PROVIDERS SUPPORTÉS:\n`;
  providers.validProviders.forEach(provider => {
    report += `  - ${provider}\n`;
  });
  report += `\n`;

  report += `💡 RECOMMANDATIONS:\n`;
  providers.recommendations.forEach(rec => {
    report += `  ${rec}\n`;
  });

  report += `\n🎯 STATUT: ${validation.isValid ? '✅ CONFIGURATION PARFAITE' : '❌ CORRECTIONS NÉCESSAIRES'}`;

  return report;
};

/**
 * 🔍 Validation rapide pour le développement
 */
export const quickValidation = (): boolean => {
  const checks = [
    SERVER_CONFIG.BACKEND_URL.includes('localhost:5001'),
    SERVER_CONFIG.OLLAMA_URL.includes('*********:11434'),
    !SERVER_CONFIG.OLLAMA_URL.includes('localhost:11434'), // Pas de local Ollama
  ];
  
  const isValid = checks.every(check => check);
  
  if (isValid) {
    console.log('✅ Configuration rapide: PARFAITE');
  } else {
    console.warn('⚠️ Configuration rapide: PROBLÈMES DÉTECTÉS');
  }
  
  return isValid;
};

// Export par défaut
export default {
  validateConfiguration,
  validateProviders,
  generateConfigReport,
  quickValidation
};
