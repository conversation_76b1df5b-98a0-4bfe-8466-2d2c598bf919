import React, { useState, useEffect } from 'react';
import { Search, Globe, Download, CheckCircle, AlertCircle } from 'lucide-react';

interface SearchAnimationProps {
  isSearching: boolean;
  query: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface SearchStep {
  id: string;
  label: string;
  icon: React.ReactNode;
  status: 'pending' | 'active' | 'completed' | 'error';
  duration?: number;
}

const SearchAnimation: React.FC<SearchAnimationProps> = ({
  isSearching,
  query,
  onComplete,
  onError
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<SearchStep[]>([
    {
      id: 'search',
      label: 'Recherche sur internet...',
      icon: <Search className="w-4 h-4" />,
      status: 'pending',
      duration: 2000
    },
    {
      id: 'extract',
      label: 'Extraction du contenu des pages...',
      icon: <Download className="w-4 h-4" />,
      status: 'pending',
      duration: 3000
    },
    {
      id: 'analyze',
      label: 'Analyse et formatage...',
      icon: <Globe className="w-4 h-4" />,
      status: 'pending',
      duration: 1000
    }
  ]);

  useEffect(() => {
    if (!isSearching) {
      setCurrentStep(0);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
      return;
    }

    let stepIndex = 0;
    const interval = setInterval(() => {
      if (stepIndex < steps.length) {
        setSteps(prev => prev.map((step, index) => ({
          ...step,
          status: index === stepIndex ? 'active' : 
                 index < stepIndex ? 'completed' : 'pending'
        })));
        
        setCurrentStep(stepIndex);
        stepIndex++;
      } else {
        clearInterval(interval);
        // Marquer la dernière étape comme complétée
        setSteps(prev => prev.map(step => ({ ...step, status: 'completed' })));
        setTimeout(() => {
          onComplete?.();
        }, 500);
      }
    }, 1500);

    return () => clearInterval(interval);
  }, [isSearching, onComplete]);

  if (!isSearching) return null;

  return (
    <div className="bg-white dark:bg-black border-2 border-black dark:border-white rounded-xl p-4 mb-4 animate-in slide-in-from-bottom-2 duration-300 shadow-lg">
      {/* Header */}
      <div className="flex items-center gap-3 mb-4">
        <div className="relative">
          <div className="w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center">
            <Search className="w-4 h-4 text-white dark:text-black" />
          </div>
          <div className="absolute -inset-1 bg-black dark:bg-white rounded-full animate-ping opacity-20"></div>
        </div>
        <div>
          <h3 className="font-medium text-black dark:text-white">
            Mode Recherche Internet
          </h3>
          <p className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-xs">
            "{query}"
          </p>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center gap-3">
            {/* Icon */}
            <div className={`
              w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300
              ${step.status === 'pending' ? 'bg-gray-300 dark:bg-gray-600 text-gray-500' :
                step.status === 'active' ? 'bg-black dark:bg-white text-white dark:text-black animate-pulse' :
                step.status === 'completed' ? 'bg-black dark:bg-white text-white dark:text-black' :
                'bg-red-500 text-white'}
            `}>
              {step.status === 'completed' ? (
                <CheckCircle className="w-3 h-3" />
              ) : step.status === 'error' ? (
                <AlertCircle className="w-3 h-3" />
              ) : (
                step.icon
              )}
            </div>

            {/* Label */}
            <span className={`
              text-sm transition-colors duration-300
              ${step.status === 'pending' ? 'text-gray-500 dark:text-gray-400' :
                step.status === 'active' ? 'text-blue-700 dark:text-blue-300 font-medium' :
                step.status === 'completed' ? 'text-green-700 dark:text-green-300' :
                'text-red-700 dark:text-red-300'}
            `}>
              {step.label}
            </span>

            {/* Loading dots for active step */}
            {step.status === 'active' && (
              <div className="flex gap-1 ml-auto">
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="mt-4">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
          <div 
            className="bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 rounded-full transition-all duration-500 ease-out"
            style={{ 
              width: `${((currentStep + 1) / steps.length) * 100}%` 
            }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>Recherche en cours...</span>
          <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
        </div>
      </div>
    </div>
  );
};

export default SearchAnimation;
