/**
 * 🛠️ Tool Use Manager - Gestion intelligente des tools
 * 
 * Responsabilités :
 * - Gestion hybride : mode recherche obligatoire + mode autonome
 * - Intégration avec les providers (Ollama, LM Studio)
 * - Exécution des tool calls
 * - Formatage des réponses
 */

import { internetSearchToolManager, INTERNET_SEARCH_TOOL } from './InternetSearchTool';
import { ToolManager } from './ToolManager';
import type { Tool } from '../../../db';
import type { ClaraAIConfig } from '../../../types/clara_assistant_types';
import { logger, LogCategory } from '../../../utils/logger';

/**
 * Configuration du tool use
 */
export interface ToolUseConfig {
  /** Mode recherche forcé - le LLM DOIT utiliser search_internet */
  forceSearchMode: boolean;
  /** Provider utilisé (pour adapter le format) */
  provider: 'ollama' | 'lm_studio';
  /** Modèle utilisé */
  model: string;
}

/**
 * Résultat d'un tool call
 */
export interface ToolCallResult {
  toolName: string;
  success: boolean;
  content?: string;
  error?: string;
  executionTime?: number;
}

/**
 * Gestionnaire principal du tool use
 */
export class ToolUseManager {
  private toolManager: ToolManager;

  constructor() {
    this.toolManager = new ToolManager();
  }

  /**
   * Obtenir les tools disponibles selon la configuration
   */
  public async getAvailableTools(config: ToolUseConfig, claraConfig: ClaraAIConfig): Promise<any[]> {
    const tools: any[] = [];

    try {
      // 1. Tool search_internet (toujours disponible)
      const searchTool = internetSearchToolManager.getOpenAIToolFormat();
      console.log('🔍 [TOOL-DEBUG] Search tool from manager:', JSON.stringify(searchTool, null, 2));
      tools.push(searchTool);

      // 2. Autres tools de la DB si nécessaire
      const dbTools = await this.toolManager.getAvailableTools(claraConfig);
      const formattedDbTools = dbTools
        .filter(tool => tool.id !== 'search_internet') // Éviter les doublons
        .map(tool => this.formatToolForOpenAI(tool));
      
      tools.push(...formattedDbTools);

      logger.info(LogCategory.TOOLS, `🔧 ${tools.length} tools disponibles (search_internet + ${dbTools.length} DB tools)`);
      
      return tools;
    } catch (error) {
      logger.error(LogCategory.TOOLS, `❌ Erreur récupération tools: ${error}`);
      return [internetSearchToolManager.getOpenAIToolFormat()]; // Au minimum search_internet
    }
  }

  /**
   * Générer le prompt système selon la configuration
   */
  public generateSystemPrompt(config: ToolUseConfig, basePrompt?: string): string {
    let systemPrompt = basePrompt || '';

    if (config.forceSearchMode) {
      // Mode recherche forcé - TOUJOURS rechercher
      systemPrompt += `\n\n🔍 MODE RECHERCHE ACTIVÉ: Tu DOIS OBLIGATOIREMENT utiliser la fonction search_internet pour répondre à cette question. Effectue une ou plusieurs recherches pertinentes pour obtenir les informations les plus récentes et précises. C'est OBLIGATOIRE, même si tu penses connaître la réponse.`;
    }

    systemPrompt += `\n\nQuand tu utilises search_internet:
- Formule des requêtes de recherche précises et pertinentes
- Choisis le bon type de recherche (general, technical, news, shopping)
- Utilise les résultats pour enrichir ta réponse
- Cite les sources quand c'est pertinent`;

    return systemPrompt.trim();
  }

  /**
   * Exécuter un tool call
   */
  public async executeToolCall(toolCall: any): Promise<ToolCallResult> {
    const startTime = Date.now();
    const functionName = toolCall.function?.name || toolCall.name;

    try {
      logger.info(LogCategory.TOOLS, `🔧 Exécution tool: ${functionName}`);

      // Parsing des arguments
      let args: any;
      try {
        const argsString = toolCall.function?.arguments || toolCall.arguments;
        args = typeof argsString === 'string' ? JSON.parse(argsString) : argsString;
      } catch (parseError) {
        return {
          toolName: functionName,
          success: false,
          error: `Failed to parse tool arguments: ${parseError}`,
          executionTime: Date.now() - startTime
        };
      }

      // Exécution selon le type de tool
      if (functionName === 'search_internet') {
        const result = await internetSearchToolManager.executeSearch(args);
        return {
          toolName: functionName,
          success: result.success,
          content: result.content,
          error: result.error,
          executionTime: Date.now() - startTime
        };
      } else {
        // Autres tools via ToolManager
        const dbResult = await this.toolManager.executeSingleTool(functionName, args);
        return {
          toolName: functionName,
          success: dbResult.success,
          content: dbResult.result ? JSON.stringify(dbResult.result) : undefined,
          error: dbResult.error,
          executionTime: Date.now() - startTime
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(LogCategory.TOOLS, `❌ Tool execution failed: ${errorMessage}`);
      
      return {
        toolName: functionName,
        success: false,
        error: errorMessage,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Formater un tool DB au format OpenAI
   */
  private formatToolForOpenAI(tool: Tool): any {
    // 🛠️ SÉCURITÉ: Vérifier que parameters existe et est un tableau
    const parameters = tool.parameters || [];

    return {
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object',
          properties: parameters.reduce((acc: any, param: any) => ({
            ...acc,
            [param.name]: {
              type: param.type.toLowerCase(),
              description: param.description
            }
          }), {}),
          required: parameters
            .filter((p: any) => p.required)
            .map((p: any) => p.name)
        }
      }
    };
  }

  /**
   * Vérifier si un message nécessite une recherche (mode autonome)
   */
  public shouldTriggerSearch(message: string): boolean {
    const searchTriggers = [
      // Questions techniques
      /compatible|compatibility|spécifications|specs|requirements/i,
      // Informations récentes
      /2024|2025|récent|latest|nouveau|new|actuel/i,
      // Comparaisons produits
      /meilleur|best|compare|comparison|vs|versus/i,
      // Prix et disponibilité
      /prix|price|cost|acheter|buy|disponible|available/i,
      // Actualités
      /actualité|news|événement|event|annonce/i,
      // Questions ouvertes nécessitant des données
      /comment|how|pourquoi|why|quand|when|où|where/i
    ];

    return searchTriggers.some(trigger => trigger.test(message));
  }
}

// Instance singleton
export const toolUseManager = new ToolUseManager();
