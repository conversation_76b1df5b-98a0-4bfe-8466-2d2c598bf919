/**
 * 🚀 Clara API Service - Version Refactorisée
 * 
 * Service principal ultra-simplifié utilisant une architecture modulaire.
 * 
 * Responsabilités :
 * - Orchestration des modules spécialisés
 * - Interface publique unifiée
 * - Gestion des erreurs globales
 * - Coordination des opérations complexes
 */

import type {
  ClaraMessage,
  ClaraFileAttachment,
  ClaraProvider,
  ClaraModel,
  ClaraAIConfig
} from '../../types/clara_assistant_types';

// Import des modules spécialisés
import { providerManager } from './providers/ProviderManager';
import { chatManager, type ChatContext } from './chat/ChatManager';
import { toolManager } from './tools/ToolManager';
import { contextManager } from './context/ContextManager';
import { attachmentProcessor } from './context/AttachmentProcessor';

export class ClaraApiService {
  private currentProvider: ClaraProvider | null = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  // ============================================================================
  // INITIALISATION
  // ============================================================================

  /**
   * Initialiser le service
   */
  private async initialize(): Promise<void> {
    try {
      console.log('🚀 Initialisation ClaraApiService...');
      
      // Charger le provider primaire
      const primaryProvider = await providerManager.getPrimaryProvider();
      if (primaryProvider) {
        this.updateProvider(primaryProvider);
      }

      this.isInitialized = true;
      console.log('✅ ClaraApiService initialisé');
    } catch (error) {
      console.error('❌ Erreur initialisation ClaraApiService:', error);
    }
  }

  /**
   * Mettre à jour le provider actuel
   */
  public updateProvider(provider: ClaraProvider): void {
    this.currentProvider = provider;
    chatManager.updateProvider(provider);
    console.log(`✅ Provider mis à jour: ${provider.name}`);
  }

  // ============================================================================
  // GESTION DES PROVIDERS ET MODÈLES
  // ============================================================================

  /**
   * Obtenir tous les providers disponibles
   */
  public async getProviders(): Promise<ClaraProvider[]> {
    return providerManager.getProviders();
  }

  /**
   * Obtenir les modèles d'un provider
   */
  public async getModels(providerId?: string): Promise<ClaraModel[]> {
    return providerManager.getModels(providerId);
  }

  /**
   * Obtenir les modèles du provider actuel
   */
  public async getCurrentProviderModels(): Promise<ClaraModel[]> {
    if (!this.currentProvider) {
      return [];
    }
    return providerManager.getModels(this.currentProvider.id);
  }

  /**
   * Obtenir le provider primaire
   */
  public async getPrimaryProvider(): Promise<ClaraProvider | null> {
    return providerManager.getPrimaryProvider();
  }

  /**
   * Vérifier la santé d'un provider
   */
  public async checkProviderHealth(provider: ClaraProvider): Promise<boolean> {
    return providerManager.checkProviderHealth(provider);
  }

  // ============================================================================
  // GESTION DES CONVERSATIONS
  // ============================================================================

  /**
   * Envoyer un message de chat avec toutes les fonctionnalités - ISOLÉ PAR SESSION
   */
  public async sendChatMessage(
    message: string,
    config: ClaraAIConfig,
    attachments?: ClaraFileAttachment[],
    systemPrompt?: string,
    conversationHistory?: ClaraMessage[],
    onContentChunk?: (content: string) => void,
    sessionId?: string
  ): Promise<ClaraMessage> {
    try {
      // Vérifier l'initialisation
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.currentProvider) {
        throw new Error('Aucun provider configuré. Veuillez sélectionner un provider.');
      }

      console.log(`💬 Envoi message: "${message.substring(0, 50)}..."`);

      // Changer de provider si nécessaire
      if (config.provider && config.provider !== this.currentProvider.id) {
        await this.switchToProvider(config.provider);
      }

      // Traiter les fichiers joints
      const processedAttachments = attachments && attachments.length > 0
        ? await attachmentProcessor.processAttachments(attachments)
        : [];

      // Préparer le contexte de conversation - AVEC SESSION ID
      const context = await chatManager.prepareConversationContext(
        message,
        config,
        systemPrompt,
        conversationHistory,
        processedAttachments,
        sessionId
      );

      // Optimiser le contexte si nécessaire
      if (conversationHistory && conversationHistory.length > 10) {
        const optimized = contextManager.optimizeContext(
          conversationHistory,
          context.ragContext || '',
          message
        );
        
        context.conversationHistory = optimized.optimizedHistory;
        context.ragContext = optimized.optimizedRagContext;
        
        console.log(`🧠 Contexte optimisé: ${optimized.contextStats.strategy}`);
      }

      // Déterminer si les outils sont nécessaires
      const toolsEnabled = config.features.enableTools;
      
      if (toolsEnabled) {
        const tools = await toolManager.getAvailableTools(config);
        
        if (tools.length > 0) {
          console.log(`🛠️ Envoi avec ${tools.length} outils disponibles`);
          return chatManager.sendMessageWithTools(message, context, tools, onContentChunk);
        }
      }

      // Envoi standard sans outils
      console.log('💬 Envoi message standard');
      return chatManager.sendStandardMessage(message, context, onContentChunk);

    } catch (error) {
      console.error('❌ Erreur sendChatMessage:', error);
      
      // Message d'erreur utilisateur-friendly
      return this.createErrorMessage(
        error instanceof Error ? error.message : 'Erreur inconnue',
        config
      );
    }
  }

  /**
   * Précharger un modèle pour de meilleures performances
   */
  public async preloadModel(config: ClaraAIConfig): Promise<void> {
    if (!this.currentProvider || !config.models.text) {
      return;
    }

    try {
      console.log(`🚀 Préchargement modèle: ${config.models.text}`);

      // 🚀 OPTIMISATION: Pour LM Studio, utiliser une requête encore plus minimale
      if (this.currentProvider.type === 'lmstudio') {
        // Requête ultra-minimale pour LM Studio
        await this.sendChatMessage(
          'Hi',
          { ...config, maxTokens: 1, temperature: 0 },
          [],
          undefined,
          [],
          undefined
        );
      } else {
        // Requête minimale pour les autres providers
        await this.sendChatMessage(
          'Hello',
          { ...config, maxTokens: 1 },
          [],
          undefined,
          [],
          undefined
        );
      }

      console.log('✅ Modèle préchargé');
    } catch (error) {
      console.log(`🔄 Préchargement échoué (non-critique): ${error instanceof Error ? error.message : 'Erreur'}`);
    }
  }

  /**
   * 🛑 Arrêter la génération en cours et nettoyer LM Studio
   */
  public stop(): void {
    try {
      console.log('🛑 Arrêt de la génération en cours...');

      // 1. Arrêter le chat manager
      chatManager.stopGeneration();

      // 2. Nettoyer la queue LM Studio si c'est le provider actuel
      if (this.currentProvider?.type === 'lmstudio') {
        this.cleanLMStudioQueue();
      }

      console.log('✅ Génération arrêtée et queue nettoyée');
    } catch (error) {
      console.warn('⚠️ Erreur lors de l\'arrêt:', error);
    }
  }

  // ============================================================================
  // GESTION DES FICHIERS
  // ============================================================================

  /**
   * Traiter des fichiers joints
   */
  public async processFileAttachments(
    attachments: ClaraFileAttachment[]
  ): Promise<ClaraFileAttachment[]> {
    return attachmentProcessor.processAttachments(attachments);
  }

  /**
   * Extraire le contenu textuel d'un fichier
   */
  public async extractTextFromFile(attachment: ClaraFileAttachment): Promise<string> {
    return attachmentProcessor.extractTextContent(attachment);
  }

  // ============================================================================
  // UTILITAIRES
  // ============================================================================

  /**
   * Nettoyer tous les caches
   */
  public clearCache(): void {
    console.log('🗑️ Nettoyage de tous les caches');
    providerManager.clearCache();
    toolManager.clearCache();
  }

  /**
   * Obtenir les statistiques du service
   */
  public getServiceStats(): {
    currentProvider: string | null;
    isInitialized: boolean;
    cacheStats: any;
  } {
    return {
      currentProvider: this.currentProvider?.name || null,
      isInitialized: this.isInitialized,
      cacheStats: {
        // Ici on pourrait ajouter des stats détaillées
      }
    };
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Changer vers un provider spécifique
   */
  private async switchToProvider(providerId: string): Promise<void> {
    try {
      const providers = await providerManager.getProviders();
      const targetProvider = providers.find(p => p.id === providerId);
      
      if (!targetProvider) {
        throw new Error(`Provider ${providerId} non trouvé`);
      }
      
      if (!targetProvider.isEnabled) {
        throw new Error(`Provider ${targetProvider.name} non activé`);
      }
      
      // Vérifier la santé du provider
      const isHealthy = await providerManager.checkProviderHealth(targetProvider);
      if (!isHealthy) {
        throw new Error(`Provider ${targetProvider.name} non disponible`);
      }
      
      this.updateProvider(targetProvider);
      console.log(`🔄 Basculé vers provider: ${targetProvider.name}`);
      
    } catch (error) {
      console.error(`❌ Erreur changement provider ${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Tester la santé d'un provider
   */
  async testProvider(provider: ClaraProvider): Promise<boolean> {
    try {
      console.log(`🏥 Test santé provider: ${provider.name}`);
      const isHealthy = await providerManager.checkProviderHealth(provider);
      console.log(`${isHealthy ? '✅' : '❌'} Provider ${provider.name}: ${isHealthy ? 'OK' : 'KO'}`);
      return isHealthy;
    } catch (error) {
      console.warn(`⚠️ Erreur test provider ${provider.name}:`, error);
      return false;
    }
  }



  /**
   * 🧹 Nettoyer la queue de LM Studio
   */
  private async cleanLMStudioQueue(): Promise<void> {
    try {
      // Envoyer une requête vide pour forcer LM Studio à nettoyer sa queue
      const response = await fetch('http://localhost:1234/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'any', // LM Studio accepte n'importe quel nom de modèle
          messages: [{ role: 'user', content: '' }], // Message vide
          max_tokens: 1, // Minimal
          stream: false
        }),
        signal: AbortSignal.timeout(1000) // Timeout rapide
      });

      // On s'en fiche de la réponse, on veut juste nettoyer la queue
      console.log('🧹 LM Studio queue cleanup request sent');

    } catch (error) {
      // Erreur normale - on essaie juste de nettoyer
      console.log('🧹 LM Studio queue cleanup completed (expected error)');
    }
  }

  /**
   * Créer un message d'erreur
   */
  private createErrorMessage(error: string, config: ClaraAIConfig): ClaraMessage {
    return {
      id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      role: 'assistant',
      content: `WeMa IA a rencontré une erreur : ${error}. Veuillez réessayer.`,
      timestamp: new Date(),
      metadata: {
        model: config.models.text || 'unknown',
        tokens: 0,
        error: true,
        errorMessage: error
      }
    };
  }
}

// Export singleton pour compatibilité avec l'existant
export const claraApiService = new ClaraApiService();
